/**
 * GraphQL权限规则定义
 * 定义各种权限检查规则
 */

const { rule, and, or, not } = require('graphql-shield');
const { USER_ROLES, AUTH_TYPES, WHATSAPP_TOKEN_ALLOWED_OPERATIONS } = require('./constants');
const logger = require('../../helpers/logger');

/**
 * JWT认证规则 - 检查是否为标准JWT认证用户
 */
const isJwtAuthenticated = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const isAuth = ctx.req.isAuth === true;
    if (!isAuth) {
      logger.warn(`未认证用户尝试访问: ${info.fieldName}`);
    }
    return isAuth || new Error('需要标准的JWT用户认证');
  }
);

/**
 * X-WhatsAppW-Token认证规则 - 仅限制来自web页面的特定操作
 */
const isWebWhatsAppToken = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // 检查是否有X-WhatsAppW-Token认证
    const hasWhatsAppAuth = ctx.whatsAppAuth === true && !!ctx.whatsAppCustomerId;

    if (!hasWhatsAppAuth) {
      logger.warn(`无效的X-WhatsAppW-Token尝试访问: ${info.fieldName}`);
      return new Error('需要有效的X-WhatsAppW-Token认证');
    }

    // 检查当前操作是否在允许列表中
    if (!WHATSAPP_TOKEN_ALLOWED_OPERATIONS.includes(info.fieldName)) {
      logger.warn(`X-WhatsAppW-Token尝试访问未授权操作: ${info.fieldName}`);
      return new Error(`X-WhatsAppW-Token不允许访问操作: ${info.fieldName}`);
    }

    logger.debug(`X-WhatsAppW-Token认证成功: ${info.fieldName}, customerId: ${ctx.whatsAppCustomerId}`);
    return true;
  }
);

/**
 * 
 * 内部调用规则 - 允许通过customerId参数的内部调用
 */
const allowInternalCall = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    // 如果有customerId参数，说明是内部调用，直接允许
    if (args.customerId) {
      logger.debug(`内部调用: ${info.fieldName}, customerId: ${args.customerId}`);
      return true;
    }

    // 否则拒绝
    return new Error('此操作仅允许内部调用');
  }
);

/**
 * 管理员权限规则
 */
const isAdmin = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const isAdminUser = ctx.req.userType === USER_ROLES.ADMIN;
    if (!isAdminUser) {
      logger.warn(`非管理员用户尝试访问管理员功能: ${info.fieldName}, 用户类型: ${ctx.req.userType}`);
    }
    return isAdminUser || new Error('需要管理员权限');
  }
);

/**
 * 餐厅用户权限规则 - 纯粹的餐厅角色检查
 */
const isRestaurant = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const userType = ctx.req.userType;
    const isRestaurantUser = userType === USER_ROLES.RESTAURANT;
    if (!isRestaurantUser) {
      logger.warn(`非餐厅用户尝试访问餐厅功能: ${info.fieldName}, 用户类型: ${userType}`);
    }
    return isRestaurantUser || new Error('仅餐厅用户可访问');
  }
);

/**
 * 客户用户权限规则 - 支持JWT和X-WhatsAppW-Token认证的客户
 */
const isCustomer = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const userType = ctx.req.userType;
    const whatsAppCustomerId = ctx.whatsAppCustomerId;

    // JWT认证的客户用户
    const isJwtCustomer = userType === USER_ROLES.CUSTOMER;

    // WhatsApp认证的客户用户
    const isWhatsAppCustomer = userType === USER_ROLES.WHATSAPP_CUSTOMER && !!whatsAppCustomerId && ctx.whatsAppAuth === true;

    const isCustomerUser = isJwtCustomer || isWhatsAppCustomer;

    if (!isCustomerUser) {
      logger.warn(`非客户用户尝试访问客户功能: ${info.fieldName}, 用户类型: ${userType}, WhatsApp认证: ${!!whatsAppCustomerId}`);
    }

    return isCustomerUser || new Error('仅客户用户可访问');
  }
);

/**
 * WhatsApp客户权限规则 - 专门针对WhatsApp认证的客户
 */
const isWhatsAppCustomer = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const userType = ctx.req.userType;
    const whatsAppCustomerId = ctx.whatsAppCustomerId;

    const isWhatsAppCustomer = userType === USER_ROLES.WHATSAPP_CUSTOMER &&
                               !!whatsAppCustomerId &&
                               ctx.whatsAppAuth === true;

    if (!isWhatsAppCustomer) {
      logger.warn(`非WhatsApp客户尝试访问WhatsApp专用功能: ${info.fieldName}, 用户类型: ${userType}, WhatsApp认证: ${!!whatsAppCustomerId}`);
    }

    return isWhatsAppCustomer || new Error('仅WhatsApp客户可访问');
  }
);

/**
 * 资源所有权规则 - 检查用户是否有权访问特定资源
 */
const isResourceOwner = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const { restaurantId, userId, userType } = ctx.req;
    
    // 管理员可以访问所有资源
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }
    
    // 餐厅用户只能访问自己的餐厅资源
    if (userType === USER_ROLES.RESTAURANT) {
      // 检查餐厅相关参数
      if (args.restaurant && args.restaurant !== restaurantId) {
        logger.warn(`餐厅用户尝试访问其他餐厅资源: ${info.fieldName}, 请求餐厅: ${args.restaurant}, 用户餐厅: ${restaurantId}`);
        return new Error('无权访问其他餐厅的资源');
      }
      if (args.restaurantId && args.restaurantId !== restaurantId) {
        logger.warn(`餐厅用户尝试访问其他餐厅资源: ${info.fieldName}, 请求餐厅: ${args.restaurantId}, 用户餐厅: ${restaurantId}`);
        return new Error('无权访问其他餐厅的资源');
      }
    }
    
    // 客户用户只能访问自己的资源
    if (userType === USER_ROLES.CUSTOMER || userType === USER_ROLES.WHATSAPP_CUSTOMER) {
      if (args.userId && args.userId !== userId) {
        logger.warn(`客户用户尝试访问其他用户资源: ${info.fieldName}, 请求用户: ${args.userId}, 当前用户: ${userId}`);
        return new Error('无权访问其他用户的资源');
      }
    }
    
    // 默认拒绝访问以确保安全
    logger.warn(`资源所有权检查未能确定权限: ${info.fieldName}, 用户: ${userId}, 类型: ${userType}, 默认拒绝访问`);
    return false;
  }
);

/**
 * 订单所有权规则 - 检查用户是否有权访问特定订单
 */
const isOrderOwner = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const { restaurantId, userId, userType } = ctx.req;

    // 管理员可以访问所有订单
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }

    const orderId = args.orderId || args.id;

    // 如果没有订单ID参数，允许通过（可能是列表查询）
    if (!orderId) {
      return true;
    }

    try {
      // 导入Order模型进行数据库查询
      const Order = require('../../models/order');

      // 查询订单信息
      const order = await Order.findById(orderId);
      if (!order) {
        logger.warn(`尝试访问不存在的订单: ${orderId}`);
        return new Error('订单不存在');
      }

      // 餐厅用户只能访问自己餐厅的订单
      if (userType === USER_ROLES.RESTAURANT) {
        if (order.restaurantId !== restaurantId) {
          logger.warn(`餐厅用户尝试访问其他餐厅订单: ${orderId}, 订单餐厅: ${order.restaurantId}, 用户餐厅: ${restaurantId}`);
          return new Error('只能访问自己餐厅的订单');
        }
        return true;
      }

      // 客户用户只能访问自己的订单
      if (userType === USER_ROLES.CUSTOMER || userType === USER_ROLES.WHATSAPP_CUSTOMER) {
        if (order.customerId !== userId) {
          logger.warn(`客户用户尝试访问其他用户订单: ${orderId}, 订单客户: ${order.customerId}, 当前用户: ${userId}`);
          return new Error('只能访问自己的订单');
        }
        return true;
      }

      // 其他用户类型默认拒绝
      logger.warn(`未知用户类型尝试访问订单: ${info.fieldName}, 用户: ${userId}, 类型: ${userType}`);
      return new Error('无权访问订单');

    } catch (error) {
      logger.error(`订单所有权检查失败: ${error.message}`);
      return new Error('订单访问权限检查失败');
    }
  }
);

/**
 * 自我管理规则 - 用户可以管理自己的信息
 */
const isSelfManagement = rule({ cache: 'contextual' })(
  async (parent, args, ctx, info) => {
    const { userId, userType } = ctx.req;
    const whatsAppCustomerId = ctx.whatsAppCustomerId;

    // 管理员可以管理所有用户
    if (userType === USER_ROLES.ADMIN) {
      return true;
    }

    // 确定当前用户ID（JWT认证或X-WhatsAppW-Token认证）
    const currentUserId = userId || whatsAppCustomerId;

    if (!currentUserId) {
      logger.warn(`自我管理检查无法确定用户身份: ${info.fieldName}, JWT用户: ${userId}, WhatsApp用户: ${whatsAppCustomerId}`);
      return new Error('无法确定用户身份');
    }

    // 用户只能管理自己
    if (args.userId && args.userId !== currentUserId) {
      logger.warn(`用户尝试管理其他用户信息: ${info.fieldName}, 请求用户: ${args.userId}, 当前用户: ${currentUserId}`);
      return new Error('只能管理自己的信息');
    }

    // 对于没有userId参数的操作（如addCustomerAddress），检查是否为自我管理操作
    if (!args.userId) {
      // 对于X-WhatsAppW-Token认证的用户，允许自我管理操作
      if (whatsAppCustomerId) {
        logger.debug(`X-WhatsAppW-Token用户自我管理操作: ${info.fieldName}, 用户: ${whatsAppCustomerId}`);
        return true;
      }

      // 对于JWT认证的用户，也允许自我管理操作
      if (userId && (userType === USER_ROLES.CUSTOMER || userType === USER_ROLES.WHATSAPP_CUSTOMER || userType === USER_ROLES.RESTAURANT)) {
        logger.debug(`JWT用户自我管理操作: ${info.fieldName}, 用户: ${userId}, 类型: ${userType}`);
        return true;
      }

      logger.warn(`自我管理检查失败: ${info.fieldName}, 用户: ${currentUserId}, 类型: ${userType}`);
      return new Error('自我管理权限检查失败');
    }

    return true;
  }
);

module.exports = {
  isJwtAuthenticated,
  isWebWhatsAppToken,
  allowInternalCall,
  isAdmin,
  isRestaurant,
  isCustomer,
  isWhatsAppCustomer,
  isResourceOwner,
  isOrderOwner,
  isSelfManagement
};
