/**
 * GraphQL权限控制常量定义
 * 定义用户角色、权限级别和认证类型
 */

// 用户角色定义
const USER_ROLES = {
  ADMIN: 'ADMIN',
  RESTAURANT: 'RESTAURANT',
  CUSTOMER: 'default',           // 传统JWT认证的客户
  WHATSAPP_CUSTOMER: 'whatsapp_customer',  // WhatsApp认证的客户
  RIDER: 'RIDER',
  VENDOR: 'VENDOR'
};

// 权限级别
const PERMISSION_LEVELS = {
  PUBLIC: 'PUBLIC',                    // 公开访问，无需认证
  AUTHENTICATED: 'AUTHENTICATED',      // 需要任意类型的认证
  CUSTOMER_ONLY: 'CUSTOMER_ONLY',      // 仅客户可访问
  RESTAURANT_ONLY: 'RESTAURANT_ONLY',  // 仅餐厅可访问
  ADMIN_ONLY: 'ADMIN_ONLY',           // 仅管理员可访问
  RESOURCE_OWNER: 'RESOURCE_OWNER'     // 资源所有者权限
};

// 认证类型（简化）
const AUTH_TYPES = {
  JWT: 'JWT',
  WEB_WHATSAPP: 'WEB_WHATSAPP'  // 只保留真正需要的X-WhatsAppW-Token认证
};

// 权限操作类型
const PERMISSION_ACTIONS = {
  CREATE: 'CREATE',
  READ: 'READ',
  UPDATE: 'UPDATE',
  DELETE: 'DELETE',
  MANAGE: 'MANAGE'
};

// 资源类型
const RESOURCE_TYPES = {
  USER: 'USER',
  RESTAURANT: 'RESTAURANT',
  ORDER: 'ORDER',
  FOOD: 'FOOD',
  CATEGORY: 'CATEGORY',
  CONFIGURATION: 'CONFIGURATION',
  DASHBOARD: 'DASHBOARD'
};

// 角色权限映射
const ROLE_PERMISSIONS = {
  [USER_ROLES.ADMIN]: {
    permissions: ['*'], // 管理员拥有所有权限
    resources: ['*'],
    description: '系统管理员，拥有所有权限'
  },
  [USER_ROLES.RESTAURANT]: {
    permissions: [
      `${RESOURCE_TYPES.RESTAURANT}:${PERMISSION_ACTIONS.MANAGE}`,
      `${RESOURCE_TYPES.ORDER}:${PERMISSION_ACTIONS.READ}`,
      `${RESOURCE_TYPES.ORDER}:${PERMISSION_ACTIONS.UPDATE}`,
      `${RESOURCE_TYPES.FOOD}:${PERMISSION_ACTIONS.MANAGE}`,
      `${RESOURCE_TYPES.CATEGORY}:${PERMISSION_ACTIONS.MANAGE}`,
      `${RESOURCE_TYPES.DASHBOARD}:${PERMISSION_ACTIONS.READ}`
    ],
    resources: ['own_restaurant'],
    description: '餐厅用户，可管理自己的餐厅'
  },
  [USER_ROLES.CUSTOMER]: {
    permissions: [
      `${RESOURCE_TYPES.USER}:${PERMISSION_ACTIONS.READ}`,
      `${RESOURCE_TYPES.USER}:${PERMISSION_ACTIONS.UPDATE}`,
      `${RESOURCE_TYPES.ORDER}:${PERMISSION_ACTIONS.CREATE}`,
      `${RESOURCE_TYPES.ORDER}:${PERMISSION_ACTIONS.READ}`,
      `${RESOURCE_TYPES.RESTAURANT}:${PERMISSION_ACTIONS.READ}`,
      `${RESOURCE_TYPES.FOOD}:${PERMISSION_ACTIONS.READ}`
    ],
    resources: ['own_profile', 'own_orders'],
    description: '普通客户，可下单和管理个人信息'
  },
  [USER_ROLES.WHATSAPP_CUSTOMER]: {
    permissions: [
      `${RESOURCE_TYPES.USER}:${PERMISSION_ACTIONS.READ}`,
      `${RESOURCE_TYPES.USER}:${PERMISSION_ACTIONS.UPDATE}`,
      `${RESOURCE_TYPES.ORDER}:${PERMISSION_ACTIONS.CREATE}`,
      `${RESOURCE_TYPES.ORDER}:${PERMISSION_ACTIONS.READ}`,
      `${RESOURCE_TYPES.RESTAURANT}:${PERMISSION_ACTIONS.READ}`,
      `${RESOURCE_TYPES.FOOD}:${PERMISSION_ACTIONS.READ}`
    ],
    resources: ['own_profile', 'own_orders', 'own_addresses'],
    description: 'WhatsApp客户，通过WhatsApp认证，可下单和管理地址'
  },
  [USER_ROLES.RIDER]: {
    permissions: [
      `${RESOURCE_TYPES.ORDER}:${PERMISSION_ACTIONS.READ}`,
      `${RESOURCE_TYPES.ORDER}:${PERMISSION_ACTIONS.UPDATE}`
    ],
    resources: ['assigned_orders'],
    description: '配送员，可查看和更新分配的订单'
  }
};

// 敏感操作列表（需要特殊权限检查）
const SENSITIVE_OPERATIONS = {
  QUERIES: [
    'users',
    'configuration',
    'getDashboardTotal',
    'allOrders',
    'assignedOrders',
    'deliveredOrders',
    'undeliveredOrders',
    'getOrderStatuses',
    'getPaymentStatuses',
    'vendors',
    'getVendor',
    'earnings',
    'withdrawRequests',
    'getAllWithdrawRequests',
    'rider',
    'riders',
    'ridersByZone',
    'riderCompletedOrders',
    'riderEarnings',
    'riderOrders',
    'riderWithdrawRequests',
    'availableRiders',
    'unassignedOrdersByZone',
    'lastOrderCreds'
  ],
  MUTATIONS: [
    'Deactivate',
    'saveConfiguration',
    'saveEmailConfiguration',
    'saveFormEmailConfiguration',
    'saveSendGridConfiguration',
    'savePaypalConfiguration',
    'saveStripeConfiguration',
    'saveTwilioConfiguration',
    'saveFirebaseConfiguration',
    'saveSentryConfiguration',
    'saveGoogleApiKeyConfiguration',
    'saveCloudinaryConfiguration',
    'saveAmplitudeApiKeyConfiguration',
    'saveGoogleClientIDConfiguration',
    'saveWebConfiguration',
    'saveAppConfigurations',
    'saveDemoConfiguration',
    'saveCurrencyConfiguration',
    'saveDeliveryRateConfiguration',
    'saveVerificationsToggle',
    'updateCommission',
    'createVendor',
    'editVendor',
    'deleteVendor',
    'uploadToken',
    'vendorResetPassword',
    'createZone',
    'editZone',
    'deleteZone',
    'createOffer',
    'editOffer',
    'deleteOffer',
    'addRestaurantToOffer',
    'createSection',
    'editSection',
    'deleteSection',
    'createTaxation',
    'editTaxation',
    'createTipping',
    'editTipping',
    'createCoupon',
    'editCoupon',
    'deleteCoupon',
    'createBanner',
    'editBanner',
    'deleteBanner',
    'createCuisine',
    'editCuisine',
    'deleteCuisine',
    'createBrand',
    'updateBrand',
    'deleteBrand',
    'addRestaurantToBrand',
    'removeRestaurantFromBrand',
    'createRider',
    'editRider',
    'deleteRider',
    'toggleAvailablity',
    'assignOrder',
    'assignRider',
    'updateOrderStatusRider',
    'updateRiderLocation',
    'notifyRiders',
    'createEarning',
    'createWithdrawRequest',
    'updateWithdrawReqStatus',
    'sendNotificationUser',
    'deleteUser',
    'deleteRestaurant'
  ]
};

// X-WhatsAppW-Token允许的操作（限制列表）
const WHATSAPP_TOKEN_ALLOWED_OPERATIONS = [
  // 查询操作
  'customerAddresses',
  'getAddressFromPostcode',
  'getSessionByToken',

  // 变更操作
  'placeOrderWhatsApp',      // 提交订单
  'addCustomerAddress',      // 添加地址
  'updateCustomerAddress',   // 更新地址
  'deleteCustomerAddress'    // 删除地址
];

// 公开操作（无需认证）
const PUBLIC_OPERATIONS = {
  QUERIES: [
    'restaurants',
    'restaurant',
    'restaurantPreview',
    'restaurantsPreview',
    'restaurantList',
    'restaurantListPreview',
    'categories',
    'foods',
    'foodByCategory',
    'foodByIds',
    'banners',
    'bannerActions',
    'cuisines',
    'addons',
    'options',
    'taxes',
    'tips',
    'zones',
    'zone',
    'coupons',
    'offers',
    'sections',
    'getCountries',
    'getCountryByIso',
    'popularItems',
    'relatedItems',
    'nearByRestaurants',
    'nearByRestaurantsPreview',
    'topRatedVendors',
    'topRatedVendorsPreview',
    'recentOrderRestaurants',
    'recentOrderRestaurantsPreview',
    'mostOrderedRestaurants',
    'mostOrderedRestaurantsPreview',
    'brand',
    'brands'
  ],
  MUTATIONS: [
    'createUser',
    'login',
    'adminLogin',
    'ownerLogin',
    'restaurantLogin',
    'riderLogin',
    'sendFormSubmission',
    'sendOtpToEmail',
    'sendOtpToPhoneNumber',
    'emailExist',
    'phoneExist',
    'forgotPassword',
    'resetPassword'
  ]
};

module.exports = {
  USER_ROLES,
  PERMISSION_LEVELS,
  AUTH_TYPES,
  PERMISSION_ACTIONS,
  RESOURCE_TYPES,
  ROLE_PERMISSIONS,
  SENSITIVE_OPERATIONS,
  WHATSAPP_TOKEN_ALLOWED_OPERATIONS,
  PUBLIC_OPERATIONS
};
